#property strict

#include "TradingPipelineDriverBase.mqh"
#include "../MQL4Logger/FileLog.mqh"

//+------------------------------------------------------------------+
//| 常量定義                                                         |
//+------------------------------------------------------------------+
#define TRADING_PIPELINE_DRIVER_NAME "TradingPipelineDriver"
#define TRADING_PIPELINE_DRIVER_TYPE "PipelineDriver"
#define DEFAULT_MAX_CONTAINERS 20
#define DEFAULT_MAX_REGISTRATIONS 100

// CFileLog 相關常量
#define DEFAULT_LOG_FILE_NAME "TradingPipelineDriver.log"
#define DEFAULT_LOG_LEVEL INFO
#define DEFAULT_PRINT_TO_CONSOLE true
#define DEFAULT_APPEND_TO_EXISTING true
#define CFILELOG_REGISTRY_KEY "MainCFileLog"
#define TRADING_ERROR_HANDLER_REGISTRY_KEY "MainTradingErrorHandler"

//+------------------------------------------------------------------+
//| 交易流水線驅動器 - 單例模式                                     |
//| 繼承自 TradingPipelineDriverBase 抽象基類                       |
//| 實現具體的初始化和配置邏輯                                       |
//+------------------------------------------------------------------+
class TradingPipelineDriver : public TradingPipelineDriverBase
{
private:
    static TradingPipelineDriver* s_instance;           // 單例實例

    // 私有構造函數（單例模式）
    TradingPipelineDriver()
        : TradingPipelineDriverBase(TRADING_PIPELINE_DRIVER_NAME, TRADING_PIPELINE_DRIVER_TYPE)
    {
        // 自動初始化
        if(!Initialize())
        {
            SetResult(false, "驅動器初始化失敗", ERROR_LEVEL_ERROR);
        }
    }

public:
    // 獲取單例實例
    static TradingPipelineDriver* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new TradingPipelineDriver();
        }
        return s_instance;
    }

    // 析構函數
    virtual ~TradingPipelineDriver() {}

    // 釋放單例實例
    static void ReleaseInstance()
    {
        if(s_instance != NULL)
        {
            delete s_instance;
            s_instance = NULL;
        }
    }

protected:
    // 實現抽象方法 - 初始化核心組件
    virtual bool InitializeComponents() override
    {
        // 1. 創建容器管理器
        if(!InitializeManager())
        {
            SetResult(false, "容器管理器初始化失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        // 2. 創建註冊器（依賴管理器）
        if(!InitializeRegistry())
        {
            SetResult(false, "註冊器初始化失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        // 3. 創建對象註冊器（獨立組件）
        if(!InitializeObjectRegistry())
        {
            SetResult(false, "對象註冊器初始化失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        // 4. 創建探索器（依賴註冊器）
        if(!InitializeExplorer())
        {
            SetResult(false, "探索器初始化失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        // 5. 創建錯誤處理器（獨立組件）
        if(!InitializeErrorHandler())
        {
            SetResult(false, "錯誤處理器初始化失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        SetResult(true, "所有組件初始化完成", ERROR_LEVEL_INFO);
        return true;
    }

    // 實現抽象方法 - 設置配置
    virtual bool SetupConfiguration() override
    {
        return SetupDefaultConfigurationInternal();
    }

private:
    //+------------------------------------------------------------------+
    //| 設置默認配置 - 內部實現                                         |
    //+------------------------------------------------------------------+
    bool SetupDefaultConfigurationInternal()
    {
        if(m_manager == NULL || m_registry == NULL || m_explorer == NULL || m_objectRegistry == NULL)
        {
            SetResult(false, "核心組件未初始化，無法設置默認配置", ERROR_LEVEL_ERROR);
            return false;
        }

        // 創建默認的事件容器
        bool success = true;

        // 為每個事件創建容器
        success &= CreateDefaultStageContainer(TRADING_INIT, "初始化容器");
        success &= CreateDefaultStageContainer(TRADING_TICK, "Tick處理容器");
        success &= CreateDefaultStageContainer(TRADING_DEINIT, "清理容器");

        // 為每個階段創建對應的容器（如果需要的話）
        success &= CreateDefaultStageContainerForStage(INIT_START, "初始化開始容器");
        success &= CreateDefaultStageContainerForStage(INIT_COMPLETE, "初始化完成容器");
        success &= CreateDefaultStageContainerForStage(TICK_DATA_FEED, "數據饋送容器");

        // 註冊 CFileLog 到 ObjectRegistry
        success &= RegisterCFileLogToObjectRegistry();

        if(success)
        {
            SetResult(true, "默認配置設置成功", ERROR_LEVEL_INFO);
        }
        else
        {
            SetResult(false, "默認配置設置部分失敗", ERROR_LEVEL_WARNING);
        }

        return success;
    }

    //+------------------------------------------------------------------+
    //| 初始化容器管理器                                                 |
    //+------------------------------------------------------------------+
    bool InitializeManager()
    {
        m_manager = new TradingPipelineContainerManager(
            "MainContainerManager",
            "ContainerManager",
            true,  // owned
            DEFAULT_MAX_CONTAINERS
        );

        if(m_manager == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化註冊器                                                     |
    //+------------------------------------------------------------------+
    bool InitializeRegistry()
    {
        if(m_manager == NULL)
        {
            return false;
        }

        m_registry = new TradingPipelineRegistry(
            m_manager,
            "MainRegistry",
            "PipelineRegistry",
            DEFAULT_MAX_REGISTRATIONS,
            true  // owned
        );

        if(m_registry == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化對象註冊器                                                 |
    //+------------------------------------------------------------------+
    bool InitializeObjectRegistry()
    {
        m_objectRegistry = new ObjectRegistry(
            "MainObjectRegistry",
            "ObjectRegistry",
            true  // owned - 擁有對象所有權
        );

        if(m_objectRegistry == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化探索器                                                     |
    //+------------------------------------------------------------------+
    bool InitializeExplorer()
    {
        if(m_registry == NULL)
        {
            return false;
        }

        m_explorer = new TradingPipelineExplorer(
            m_registry,
            "MainExplorer",
            "TradingPipelineExplorer",
            "主要交易流水線探索器"
        );

        if(m_explorer == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化錯誤處理器                                                 |
    //+------------------------------------------------------------------+
    bool InitializeErrorHandler()
    {
        m_errorHandler = new TradingErrorHandler();

        if(m_errorHandler == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 創建默認階段容器                                                 |
    //+------------------------------------------------------------------+
    bool CreateDefaultStageContainer(ENUM_TRADING_EVENT event, string description)
    {
        if(m_registry == NULL)
        {
            return false;
        }

        // 創建容器名稱
        string containerName = "Container_" + TradingEventUtils::EventToString(event);

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            containerName,
            description,
            "TradingPipelineContainer",
            true,  // owned
            10     // maxPipelines
        );

        if(container == NULL)
        {
            return false;
        }

        // 註冊容器
        bool registered = m_registry.Register(event, container);
        if(!registered)
        {
            delete container;
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 為特定階段創建默認容器                                           |
    //+------------------------------------------------------------------+
    bool CreateDefaultStageContainerForStage(ENUM_TRADING_STAGE stage, string description)
    {
        if(m_registry == NULL)
        {
            return false;
        }

        // 創建容器名稱
        string containerName = "Container_" + TradingEventUtils::StageToString(stage);

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            containerName,
            description,
            "TradingPipelineContainer",
            true,  // owned
            10     // maxPipelines
        );

        if(container == NULL)
        {
            return false;
        }

        // 註冊容器到階段
        bool registered = m_registry.Register(stage, container);
        if(!registered)
        {
            delete container;
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 註冊 CFileLog 到 ObjectRegistry                                  |
    //+------------------------------------------------------------------+
    bool RegisterCFileLogToObjectRegistry()
    {
        if(m_objectRegistry == NULL)
        {
            SetResult(false, "ObjectRegistry 未初始化，無法註冊 CFileLog", ERROR_LEVEL_ERROR);
            return false;
        }

        // 創建 CFileLog 實例
        CFileLog* fileLog = new CFileLog(
            DEFAULT_LOG_FILE_NAME,
            DEFAULT_LOG_LEVEL,
            DEFAULT_PRINT_TO_CONSOLE,
            DEFAULT_APPEND_TO_EXISTING
        );

        if(fileLog == NULL)
        {
            SetResult(false, "無法創建 CFileLog 實例", ERROR_LEVEL_ERROR);
            return false;
        }

        // 註冊到 ObjectRegistry
        bool success = RegisterObject(
            CFILELOG_REGISTRY_KEY,
            fileLog,
            "主要文件日誌記錄器",
            "TradingPipelineDriver 的主要日誌記錄器，用於記錄所有驅動器活動",
            "CFileLog"
        );

        if(success)
        {
            // 記錄註冊成功的消息
            fileLog.Info("CFileLog 已成功註冊到 ObjectRegistry，鍵: " + CFILELOG_REGISTRY_KEY);
            SetResult(true, "CFileLog 註冊成功", ERROR_LEVEL_INFO);
        }
        else
        {
            // 如果註冊失敗，需要手動清理
            delete fileLog;
            SetResult(false, "CFileLog 註冊失敗", ERROR_LEVEL_ERROR);
        }

        return success;
    }

    //+------------------------------------------------------------------+
    //| 獲取註冊的 CFileLog 實例                                         |
    //+------------------------------------------------------------------+
    CFileLog* GetRegisteredCFileLog()
    {
        if(m_objectRegistry == NULL)
        {
            return NULL;
        }

        void* logPtr = GetRegisteredObject(CFILELOG_REGISTRY_KEY);
        return dynamic_cast<CFileLog*>(logPtr);
    }

    //+------------------------------------------------------------------+
    //| 使用註冊的 CFileLog 記錄消息                                     |
    //+------------------------------------------------------------------+
    bool LogMessage(ENUM_LOG_LEVEL level, const string message)
    {
        CFileLog* logger = GetRegisteredCFileLog();
        if(logger == NULL)
        {
            return false;
        }

        logger.Log(level, message);
        return true;
    }

    // CFileLog 便利方法
    bool LogInfo(const string message)
    {
        return LogMessage(INFO, message);
    }

    bool LogWarning(const string message)
    {
        return LogMessage(WARNING, message);
    }

    bool LogError(const string message)
    {
        return LogMessage(ERROR, message);
    }

    bool LogDebug(const string message)
    {
        return LogMessage(DEBUG, message);
    }

    bool LogTrace(const string message)
    {
        return LogMessage(TRACE, message);
    }

    bool LogCritical(const string message)
    {
        return LogMessage(CRITICAL, message);
    }
};

//+------------------------------------------------------------------+
//| 靜態成員初始化                                                   |
//+------------------------------------------------------------------+
TradingPipelineDriver* TradingPipelineDriver::s_instance = NULL;
