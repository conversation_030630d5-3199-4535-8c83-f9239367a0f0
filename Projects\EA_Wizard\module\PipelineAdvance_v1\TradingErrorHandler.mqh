#property strict

//+------------------------------------------------------------------+
//| TradingErrorHandler class definition                             |
//| 專為 PipelineAdvance_v1 模組設計的錯誤處理器                    |
//+------------------------------------------------------------------+

// 防止重複包含
#ifndef _TRADING_ERROR_HANDLER_MQH_
#define _TRADING_ERROR_HANDLER_MQH_

// 引入必要的模組
#include "TradingEvent.mqh"

// 前向聲明
class PipelineResult;
class TradingErrorRecord;

//+------------------------------------------------------------------+
//| TradingErrorVisitor 介面 - 錯誤處理訪問者模式                   |
//+------------------------------------------------------------------+
class TradingErrorVisitor
{
public:
    // 構造函數
    TradingErrorVisitor() {}

    // 析構函數
    virtual ~TradingErrorVisitor() {}

    // 訪問錯誤記錄的方法，由子類實現
    virtual void Visit(const TradingErrorRecord& record) = 0;
};

//+------------------------------------------------------------------+
//| 錯誤記錄項目類別                                                 |
//+------------------------------------------------------------------+
class TradingErrorRecord
{
public:
    string m_message;                    // 錯誤消息
    string m_source;                     // 錯誤來源
    ENUM_ERROR_LEVEL m_errorLevel;       // 錯誤級別
    datetime m_timestamp;                // 時間戳

    // 構造函數
    TradingErrorRecord(string message = "", string source = "", ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_ERROR)
        : m_message(message), m_source(source), m_errorLevel(errorLevel), m_timestamp(TimeCurrent()) {}

    // 析構函數
    ~TradingErrorRecord() {}
};

//+------------------------------------------------------------------+
//| TradingErrorHandler 類別                                        |
//+------------------------------------------------------------------+
class TradingErrorHandler
{
private:
    string m_last_error;                 // 最後一個錯誤消息
    TradingErrorRecord* m_error_log[];   // 錯誤記錄指針陣列
    int m_error_count;                   // 錯誤計數
    int m_max_errors;                    // 最大錯誤數量

public:
    // 構造函數
    TradingErrorHandler()
    {
        m_last_error = "";
        m_error_count = 0;
        m_max_errors = 50;  // 默認最大錯誤數量
        ArrayResize(m_error_log, m_max_errors);

        // 初始化指針陣列
        for(int i = 0; i < m_max_errors; i++)
        {
            m_error_log[i] = NULL;
        }
    }

    // 析構函數
    ~TradingErrorHandler()
    {
        // 清理所有錯誤記錄對象
        for(int i = 0; i < m_error_count; i++)
        {
            if(m_error_log[i] != NULL)
            {
                delete m_error_log[i];
                m_error_log[i] = NULL;
            }
        }
        ArrayFree(m_error_log);
    }

    // 設置最大錯誤數量
    void SetMaxErrors(int maxErr)
    {
        if(maxErr > 0)
        {
            // 如果新大小小於當前錯誤數量，需要清理多餘的錯誤記錄
            if(maxErr < m_error_count)
            {
                for(int i = maxErr; i < m_error_count; i++)
                {
                    if(m_error_log[i] != NULL)
                    {
                        delete m_error_log[i];
                        m_error_log[i] = NULL;
                    }
                }
                m_error_count = maxErr;
            }

            m_max_errors = maxErr;
            ArrayResize(m_error_log, m_max_errors);

            // 初始化新增的指針位置
            for(int i = m_error_count; i < m_max_errors; i++)
            {
                m_error_log[i] = NULL;
            }
        }
    }

    // 添加錯誤 - 無輸出版本
    void AddError(string error, string source = "", ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_ERROR)
    {
        m_last_error = error;

        // 創建錯誤記錄
        TradingErrorRecord* record = new TradingErrorRecord(error, source, errorLevel);

        // 添加到錯誤日誌
        AddErrorRecord(record);
    }

    // 處理錯誤 - 使用 visitor 模式
    void HandleError(TradingErrorVisitor* visitor)
    {
        if(visitor == NULL) return;

        // 遍歷所有錯誤記錄，讓 visitor 處理
        for(int i = 0; i < m_error_count; i++)
        {
            if(m_error_log[i] != NULL)
            {
                visitor.Visit(*m_error_log[i]);
            }
        }
    }

    // 處理 PipelineResult 錯誤 - 專門方法
    void HandlePipelineResult(PipelineResult* result);  // 實現將在包含 PipelineResult 定義後提供

    // 批量處理 PipelineResult 陣列
    void HandlePipelineResults(PipelineResult* &results[], int count);  // 實現將在包含 PipelineResult 定義後提供

    // 清除所有錯誤
    void ClearErrors()
    {
        // 清理所有錯誤記錄對象
        for(int i = 0; i < m_error_count; i++)
        {
            if(m_error_log[i] != NULL)
            {
                delete m_error_log[i];
                m_error_log[i] = NULL;
            }
        }

        m_error_count = 0;
        m_last_error = "";
    }

    // 獲取最後一個錯誤
    string GetLastError() const
    {
        return m_last_error;
    }

    // 獲取第一個錯誤
    string GetFirstError() const
    {
        if(m_error_count > 0 && m_error_log[0] != NULL)
        {
            return m_error_log[0].m_message;
        }
        return "";
    }

    // 獲取錯誤計數
    int GetErrorCount() const
    {
        return m_error_count;
    }

    // 檢查是否為空
    bool IsEmpty() const
    {
        return m_error_count == 0;
    }

    // 獲取指定級別的錯誤計數
    int GetErrorCountByLevel(ENUM_ERROR_LEVEL level) const
    {
        int count = 0;
        for(int i = 0; i < m_error_count; i++)
        {
            if(m_error_log[i] != NULL && m_error_log[i].m_errorLevel == level)
                count++;
        }
        return count;
    }

    // 檢查是否有嚴重錯誤
    bool HasCriticalErrors() const
    {
        return GetErrorCountByLevel(ERROR_LEVEL_CRITICAL) > 0;
    }

    // 獲取錯誤記錄
    bool GetErrorRecord(int index, TradingErrorRecord& record) const
    {
        if(index >= 0 && index < m_error_count && m_error_log[index] != NULL)
        {
            record = *m_error_log[index];
            return true;
        }
        return false;
    }

    // 獲取所有錯誤的摘要
    string GetErrorSummary() const
    {
        string summary = StringFormat("錯誤總數: %d\n", m_error_count);
        summary += StringFormat("信息: %d, 警告: %d, 錯誤: %d, 嚴重: %d",
                               GetErrorCountByLevel(ERROR_LEVEL_INFO),
                               GetErrorCountByLevel(ERROR_LEVEL_WARNING),
                               GetErrorCountByLevel(ERROR_LEVEL_ERROR),
                               GetErrorCountByLevel(ERROR_LEVEL_CRITICAL));
        return summary;
    }

    // 彈出最後一個錯誤（移除並返回）
    string PopError()
    {
        if(m_error_count > 0)
        {
            int lastIndex = m_error_count - 1;
            string errorMessage = "";

            if(m_error_log[lastIndex] != NULL)
            {
                errorMessage = m_error_log[lastIndex].m_message;
                delete m_error_log[lastIndex];
                m_error_log[lastIndex] = NULL;
            }

            m_error_count--;

            // 更新最後錯誤
            if(m_error_count > 0 && m_error_log[m_error_count - 1] != NULL)
            {
                m_last_error = m_error_log[m_error_count - 1].m_message;
            }
            else
            {
                m_last_error = "";
            }

            return errorMessage;
        }
        return "";
    }

    // 移除第一個錯誤（移除並返回）
    string ShiftError()
    {
        if(m_error_count > 0)
        {
            string errorMessage = "";

            if(m_error_log[0] != NULL)
            {
                errorMessage = m_error_log[0].m_message;
                delete m_error_log[0];
            }

            // 向前移動所有錯誤記錄
            for(int i = 1; i < m_error_count; i++)
            {
                m_error_log[i - 1] = m_error_log[i];
            }

            m_error_count--;
            m_error_log[m_error_count] = NULL;

            // 更新最後錯誤
            if(m_error_count > 0 && m_error_log[m_error_count - 1] != NULL)
            {
                m_last_error = m_error_log[m_error_count - 1].m_message;
            }
            else
            {
                m_last_error = "";
            }

            return errorMessage;
        }
        return "";
    }

private:
    // 添加錯誤記錄到陣列
    void AddErrorRecord(TradingErrorRecord* record)
    {
        if(m_error_count < m_max_errors)
        {
            m_error_log[m_error_count] = record;
            m_error_count++;
        }
        else
        {
            // 刪除最舊的錯誤記錄
            if(m_error_log[0] != NULL)
            {
                delete m_error_log[0];
            }

            // 移動陣列元素，為新錯誤騰出空間
            for(int i = 1; i < m_error_count; i++)
            {
                m_error_log[i-1] = m_error_log[i];
            }
            m_error_log[m_max_errors - 1] = record;
        }
    }

    // 將錯誤級別轉換為字符串
    string ErrorLevelToString(ENUM_ERROR_LEVEL level) const
    {
        switch(level)
        {
            case ERROR_LEVEL_INFO:     return "INFO";
            case ERROR_LEVEL_WARNING:  return "WARNING";
            case ERROR_LEVEL_ERROR:    return "ERROR";
            case ERROR_LEVEL_CRITICAL: return "CRITICAL";
            default:                   return "UNKNOWN";
        }
    }
};



//+------------------------------------------------------------------+
//| PipelineResult 相關方法的實現                                    |
//| 注意：這些方法需要在包含 PipelineResult 定義後才能使用           |
//+------------------------------------------------------------------+

// 包含 PipelineResult 定義的宏
#ifdef PIPELINE_RESULT_DEFINED

// 處理 PipelineResult 錯誤的實現
void TradingErrorHandler::HandlePipelineResult(PipelineResult* result)
{
    if(result == NULL) return;

    if(!result.IsSuccess())
    {
        AddError(result.GetMessage(), result.GetSource(), result.GetErrorLevel());
    }
}

// 批量處理 PipelineResult 陣列的實現
void TradingErrorHandler::HandlePipelineResults(PipelineResult* &results[], int count)
{
    for(int i = 0; i < count; i++)
    {
        if(results[i] != NULL)
        {
            HandlePipelineResult(results[i]);
        }
    }
}

#endif // PIPELINE_RESULT_DEFINED

//+------------------------------------------------------------------+
//| 具體的 TradingErrorVisitor 實現                                 |
//+------------------------------------------------------------------+

// 日誌記錄 Visitor - 替代原來的 LogError 方法
class LogErrorVisitor : public TradingErrorVisitor
{
public:
    LogErrorVisitor() {}
    virtual ~LogErrorVisitor() {}

    virtual void Visit(const TradingErrorRecord& record) override
    {
        string levelStr = ErrorLevelToString(record.m_errorLevel);
        string logMessage = StringFormat("[%s] %s: %s",
                                       levelStr,
                                       record.m_source,
                                       record.m_message);

        // 使用 Print 輸出錯誤信息
        Print("TradingErrorHandler: ", logMessage);
    }

private:
    // 將錯誤級別轉換為字符串
    string ErrorLevelToString(ENUM_ERROR_LEVEL level) const
    {
        switch(level)
        {
            case ERROR_LEVEL_INFO:     return "INFO";
            case ERROR_LEVEL_WARNING:  return "WARNING";
            case ERROR_LEVEL_ERROR:    return "ERROR";
            case ERROR_LEVEL_CRITICAL: return "CRITICAL";
            default:                   return "UNKNOWN";
        }
    }
};

// 錯誤統計 Visitor
class ErrorStatisticsVisitor : public TradingErrorVisitor
{
private:
    int m_info_count;
    int m_warning_count;
    int m_error_count;
    int m_critical_count;

public:
    ErrorStatisticsVisitor()
    {
        m_info_count = 0;
        m_warning_count = 0;
        m_error_count = 0;
        m_critical_count = 0;
    }

    virtual ~ErrorStatisticsVisitor() {}

    virtual void Visit(const TradingErrorRecord& record) override
    {
        switch(record.m_errorLevel)
        {
            case ERROR_LEVEL_INFO:
                m_info_count++;
                break;
            case ERROR_LEVEL_WARNING:
                m_warning_count++;
                break;
            case ERROR_LEVEL_ERROR:
                m_error_count++;
                break;
            case ERROR_LEVEL_CRITICAL:
                m_critical_count++;
                break;
        }
    }

    // 獲取統計結果
    int GetInfoCount() const { return m_info_count; }
    int GetWarningCount() const { return m_warning_count; }
    int GetErrorCount() const { return m_error_count; }
    int GetCriticalCount() const { return m_critical_count; }
    int GetTotalCount() const { return m_info_count + m_warning_count + m_error_count + m_critical_count; }

    string GetSummary() const
    {
        return StringFormat("錯誤統計 - 信息: %d, 警告: %d, 錯誤: %d, 嚴重: %d, 總計: %d",
                           m_info_count, m_warning_count, m_error_count, m_critical_count, GetTotalCount());
    }
};

// 錯誤過濾 Visitor - 只處理特定級別的錯誤
class ErrorFilterVisitor : public TradingErrorVisitor
{
private:
    ENUM_ERROR_LEVEL m_filter_level;
    TradingErrorVisitor* m_target_visitor;

public:
    ErrorFilterVisitor(ENUM_ERROR_LEVEL filterLevel, TradingErrorVisitor* targetVisitor)
        : m_filter_level(filterLevel), m_target_visitor(targetVisitor) {}

    virtual ~ErrorFilterVisitor() {}

    virtual void Visit(const TradingErrorRecord& record) override
    {
        if(record.m_errorLevel == m_filter_level && m_target_visitor != NULL)
        {
            m_target_visitor.Visit(record);
        }
    }
};

// 結束防止重複包含
#endif // _TRADING_ERROR_HANDLER_MQH_
